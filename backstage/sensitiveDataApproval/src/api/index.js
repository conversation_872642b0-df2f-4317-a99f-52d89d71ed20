import request from '@root/publicMethods/request';

// 获取劳动者列表
// export function getEmployeeList(data) {
//   return request({
//     url: '/manage/eHealthRecord/getEmployeeList',
//     method: 'post',
//     data,
//   });
// }
export function getEmployeeList(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getEmployeeListByArea',
    method: 'get',
    params,
  });
}

// 获取行业分类
export function getIndustryCategory(params) {
  return request({
    url: '/api/adminorgGov/getIndustryCategory',
    method: 'get',
    params,
  });
}

// 获取危害因素
export function findHarmFactors(params) {
  return request({
    url: '/manage/eHealthRecord/findHarmFactors',
    method: 'get',
    params,
  });
}

// 获取地址信息
export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

// 获取数据上报审批列表
export function getDataReportApprovalList(data) {
  return request({
    url: '/manage/sensitiveDataApproval/getDataReportApprovalList',
    method: 'post',
    data,
  });
}

// 获取审批流程详情
export function getApprovalFlowDetail(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getApprovalFlowDetail',
    method: 'get',
    params,
  });
}

// 提交审批结果
export function submitApprovalResult(data) {
  return request({
    url: '/manage/sensitiveDataApproval/submitApprovalResult',
    method: 'post',
    data,
  });
}

// 获取企业列表（用于上报时搜索）
export function getEnterpriseList(data) {
  return request({
    url: '/manage/sensitiveDataApproval/getEnterpriseList',
    method: 'post',
    data,
  });
}

// 创建敏感数据审批记录（上报）
export function createApprovalRecord(data) {
  return request({
    url: '/manage/sensitiveDataApproval/createApprovalRecord',
    method: 'post',
    data,
  });
}

// 获取审批流程
export function getApprovalFlow(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getApprovalFlow',
    method: 'get',
    params,
  });
}

// 检查用户上报权限
export function getCanReport(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getCanReport',
    method: 'get',
    params,
  });
}
