import request from '@root/publicMethods/request';

// 获取劳动者列表
export function getEmployeeList(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getEmployeeListByArea',
    method: 'get',
    params,
  });
}

// 上报前，获取当前账号是否有上报权限
export function getCanReport(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getCanReport',
    method: 'get',
    params,
  });
}

// 创建敏感数据审批记录（上报）
export function createApprovalRecord(data) {
  return request({
    url: '/manage/sensitiveDataApproval/createApprovalRecord',
    method: 'post',
    data,
  });
}

// 获取数据上报审批列表
export function getDataReportApprovalList(data) {
  return request({
    url: '/manage/sensitiveDataApproval/getDataReportApprovalList',
    method: 'post',
    data,
  });
}

// 更新上报内容
export function updateApprovalRecord(data) {
  return request({
    url: '/manage/sensitiveDataApproval/updateApprovalRecord',
    method: 'post',
    data,
  });
}

// 撤销上报
export function deleteApprovalRecord(params) {
  return request({
    url: '/manage/sensitiveDataApproval/deleteApprovalRecord',
    method: 'get',
    params,
  });
}

// 获取审批流程详情
export function getApprovalFlowDetail(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getApprovalFlowDetail',
    method: 'get',
    params,
  });
}

// 提交审批结果
export function submitApprovalResult(data) {
  return request({
    url: '/manage/sensitiveDataApproval/submitApprovalResult',
    method: 'post',
    data,
  });
}


// 获取企业列表（用于上报时搜索）
export function getEnterpriseList(data) {
  return request({
    url: '/manage/sensitiveDataApproval/getEnterpriseList',
    method: 'post',
    data,
  });
}
