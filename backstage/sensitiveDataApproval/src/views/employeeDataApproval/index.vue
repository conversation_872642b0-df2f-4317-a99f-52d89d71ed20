<template>
  <div class="padding" style="height:100%">
    <TitleTag :titleName="'查询条件'"> </TitleTag>
    <div class="dataReportApproval">
      <el-row>
        <el-col :span="24">
          <div class="first-line">
            <div class="search-item">
              <span class="label-title">劳动者姓名</span>
              <el-input v-model="searchData.workerName" placeholder="请输入劳动者姓名"></el-input>
            </div>

            <div class="search-item">
              <span class="label-title">上报时间</span>
              <el-date-picker v-model="searchData.reportDateRange" type="daterange" placeholder="选择日期"
                range-separator="至" start-placeholder="起始日期" end-placeholder="结束日期">
              </el-date-picker>
            </div>

            <div class="search-item">
              <el-button type="primary" size="mini" style="margin-left: 10px;" @click="search" icon="el-icon-search"
                plain>查询</el-button>
              <el-button type="warning" size="mini" @click="reset" icon="el-icon-refresh-right" plain>重置</el-button>
              <el-button type="primary" size="mini" @click="openReportDialog" style="margin-left: 10px;">上报</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <TitleTag :titleName="'数据上报审批列表'"> </TitleTag>
    <div class="dataReportApproval">
      <div class="table">
        <el-table :data="tableData" stripe border tooltip-effect="dark"
          :header-cell-style="{ background: '#F5F7FA', fontSize: '14px', fontWeight: 700, color: '#333' }">
          <el-table-column type="index" :index="indexMethod" label="序号" align="center" width="60"></el-table-column>
          <el-table-column prop="workerName" label="劳动者姓名" align="center" width="120"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="approvalStatus" label="审批状态" align="center" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag :type="getApprovalStatusType(scope.row.approvalStatus)">
                {{ getApprovalStatusText(scope.row.approvalStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="reportReason" label="上报理由" align="center" min-width="200"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="currentApprovalNode" label="当前审批节点" align="center" width="150"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="reportTime" label="上报时间" align="center" width="150" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatDate(scope.row.reportTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="enterpriseName" label="上报单位" align="center" min-width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" align="center" width="200">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" plain @click="viewApprovalFlow(scope.row)">查看流程</el-button>
              <el-button v-if="scope.row.approvalStatus === 'pending' || scope.row.approvalStatus === 'processing'"
                type="success" size="mini" plain @click="openApprovalDialog(scope.row)" style="margin-left: 5px;">
                审批
              </el-button>
              <el-button type="info" size="mini" plain @click="viewDetail(scope.row)" style="margin-left: 5px;">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination @size-change="getData" @current-change="getData" :current-page.sync="pageInfo.pageNum"
          :page-size.sync="pageInfo.pageSize" :page-sizes="[10, 20, 30, 50, 100]" background
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 上报流程右侧抽屉 -->
    <el-drawer title="上报审批流程" :visible.sync="flowDrawerVisible" direction="rtl" size="60%"
      :close-on-press-escape="false">
      <div class="flow-content" style="padding: 20px;">
        <div class="flow-header">
          <h4>{{ currentFlowData.workerName }} - 数据上报审批流程</h4>
          <p class="flow-reason">上报理由：{{ currentFlowData.reportReason }}</p>
        </div>

        <!-- <div class="flow-steps">
          <el-steps :active="currentFlowData.currentStep" finish-status="success" align-center>
            <el-step
              v-for="(step, index) in currentFlowData.approvalSteps"
              :key="index"
              :title="step.nodeName"
              :description="step.description">
            </el-step>
          </el-steps>
        </div> -->
        <div class="flow-details">
          <h5 style="margin: 20px 0 10px 0;">审批历史</h5>
          <el-table :data="currentFlowData.approvalHistory" border>
            <el-table-column prop="nodeName" label="审批节点" align="center" width="120"></el-table-column>
            <el-table-column prop="approverName" label="审批人" align="center" width="100"></el-table-column>
            <el-table-column prop="approvalTime" label="审批时间" align="center" width="140">
              <template slot-scope="scope">
                {{ formatDate(scope.row.approvalTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="approvalResult" label="审批结果" align="center" width="80">
              <template slot-scope="scope">
                <el-tag :type="getApprovalResultType(scope.row.approvalResult)" size="mini">
                  {{ scope.row.approvalResult }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approvalComment" label="审批意见" align="center" min-width="150"
              show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>

    <!-- 审批弹窗 -->
    <el-dialog title="审批操作" :visible.sync="approvalDialogVisible" width="600px" :close-on-click-modal="false">
      <div class="approval-content">
        <div class="approval-info">
          <h4>{{ currentApprovalData.workerName }} - 审批信息</h4>
          <p><strong>上报理由：</strong>{{ currentApprovalData.reportReason }}</p>
          <p><strong>当前节点：</strong>{{ currentApprovalData.currentApprovalNode }}</p>
          <p><strong>上报时间：</strong>{{ formatDate(currentApprovalData.reportTime) }}</p>
        </div>

        <el-form :model="approvalForm" :rules="approvalRules" ref="approvalForm" label-width="100px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="approvalForm.result">
              <el-radio label="approve">通过</el-radio>
              <el-radio label="reject">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审批意见" prop="comment">
            <el-input v-model="approvalForm.comment" type="textarea" :rows="4" placeholder="请输入审批意见" maxlength="500"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApproval" :loading="approvalLoading">提交审批</el-button>
      </div>
    </el-dialog>

    <!-- 上报弹窗 -->
    <el-dialog title="数据上报" :visible.sync="reportDialogVisible" width="600px" :close-on-click-modal="false">
      <el-form :model="reportForm" :rules="reportRules" ref="reportForm" label-width="100px">
        <el-form-item label="劳动者" prop="worker">
          <el-select v-model="reportForm.worker" placeholder="请选择劳动者" style="width: 100%;">
            <el-option v-for="worker in workerList" :key="worker._id" :label="worker.name" :value="worker._id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="上报理由" prop="reason">
          <el-input v-model="reportForm.reason" type="textarea" :rows="4" placeholder="请输入上报理由" maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="reportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReport" :loading="reportLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import {
  getDataReportApprovalList,
  getApprovalFlowDetail,
  submitApprovalResult,
  getEmployeeList,
  createApprovalRecord,
  getCanReport
} from '@/api'

export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      searchData: {
        workerName: '',
        approvalStatus: '',
        reportDateRange: []
      },
      // 流程抽屉相关
      flowDrawerVisible: false,
      currentFlowData: {
        workerName: '',
        reportReason: '',
        currentStep: 0,
        approvalSteps: [],
        approvalHistory: []
      },
      // 审批弹窗相关
      approvalDialogVisible: false,
      approvalLoading: false,
      currentApprovalData: {
        _id: '',
        workerName: '',
        reportReason: '',
        currentApprovalNode: '',
        reportTime: ''
      },
      approvalForm: {
        result: '',
        comment: ''
      },
      approvalRules: {
        result: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        comment: [
          { required: true, message: '请输入审批意见', trigger: 'blur' },
          { min: 5, message: '审批意见至少5个字符', trigger: 'blur' }
        ]
      },
      // 上报弹窗相关
      reportDialogVisible: false,
      reportLoading: false,
      reportForm: {
        worker: '',
        reason: ''
      },
      reportRules: {
        worker: [
          { required: true, message: '请选择劳动者', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入上报理由', trigger: 'blur' },
        ]
      },
      workerList: [],
      dataReportAuth: false,// 是否有上报权限
    }
  },

  async created() {
    await this.checkReportPermission();
    this.getData();
  },

  methods: {
    async getData() {
      try {
        const params = {
          ...this.pageInfo,
          ...this.searchData
        };

        // 处理日期范围
        if (this.searchData.reportDateRange && this.searchData.reportDateRange.length === 2) {
          params.startDate = this.searchData.reportDateRange[0];
          params.endDate = this.searchData.reportDateRange[1];
        }
        delete params.reportDateRange;

        const res = await getDataReportApprovalList(params);
        this.tableData = res.data.list || [];
        this.total = res.data.total || 0;
      } catch (error) {
        this.$message.error('获取数据失败');
        console.error(error);
      }
    },

    search() {
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getData();
    },

    reset() {
      this.searchData = this.$options.data()['searchData'];
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getData();
    },

    async viewApprovalFlow(row) {
      try {
        const res = await getApprovalFlowDetail({ _id: row._id });

        this.currentFlowData = {
          workerName: row.workerName,
          reportReason: row.reportReason,
          currentStep: res.data.currentStep || 0,
          approvalSteps: res.data.approvalSteps || [],
          approvalHistory: res.data.approvalHistory || []
        };
        this.flowDrawerVisible = true;
      } catch (error) {
        this.$message.error('获取审批流程详情失败');
        console.error(error);
      }
    },

    // 打开审批弹窗
    openApprovalDialog(row) {
      this.currentApprovalData = {
        _id: row._id,
        workerName: row.workerName,
        reportReason: row.reportReason,
        currentApprovalNode: row.currentApprovalNode,
        reportTime: row.reportTime
      };

      // 重置表单
      this.approvalForm = {
        result: '',
        comment: ''
      };

      this.approvalDialogVisible = true;

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.approvalForm) {
          this.$refs.approvalForm.clearValidate();
        }
      });
    },

    // 提交审批
    async submitApproval() {
      try {
        // 表单验证
        const valid = await this.$refs.approvalForm.validate();
        if (!valid) return;

        this.approvalLoading = true;

        const res = await submitApprovalResult({
          _id: this.currentApprovalData._id,
          result: this.approvalForm.result,
          comment: this.approvalForm.comment
        });

        this.$message.success('审批提交成功');
        this.approvalDialogVisible = false;

        // 刷新列表数据
        this.getData();

      } catch (error) {
        this.$message.error('审批提交失败');
        console.error(error);
      } finally {
        this.approvalLoading = false;
      }
    },

    indexMethod(index) {
      return (this.pageInfo.pageNum - 1) * this.pageInfo.pageSize + (index + 1);
    },

    getApprovalStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'processing': 'primary',
        'approved': 'success',
        'rejected': 'danger'
      };
      return statusMap[status] || 'info';
    },

    getApprovalStatusText(status) {
      const statusMap = {
        'pending': '待审批',
        'processing': '审批中',
        'approved': '已通过',
        'rejected': '已拒绝'
      };
      return statusMap[status] || '未知';
    },

    getApprovalResultType(result) {
      const resultMap = {
        '通过': 'success',
        '拒绝': 'danger',
        '待审批': 'warning'
      };
      return resultMap[result] || 'info';
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 查看详情 - 跳转到电子档案详情页面
    viewDetail(row) {
      // 假设劳动者ID存储在workerId字段中，如果字段名不同请相应调整
      const workerId = row.workerId || row._id;
      // 跳转到电子档案详情页面，携带劳动者ID
      this.$router.push({
        name: 'eHealthRecordDetail', // 请根据实际路由名称调整
        query: {
          _id: workerId
        }
      });
    },

    // 获取劳动者列表
    async getWorkerList() {
      try {
        const res = await getEmployeeList({
          pageNum: 1,
          pageSize: 100 // 获取更多数据供选择
        });
        this.workerList = res.data.list.map(item => ({
          _id: item._id,
          name: item.name
        }));
      } catch (error) {
        console.error('获取劳动者列表失败:', error);
        this.$message.error('获取劳动者列表失败');
      }
    },

    // 打开上报弹窗
    async openReportDialog() {
      const res = await getCanReport({ dataModel: 'Employees' });
      this.dataReportAuth = res.data.dataReportAuth;

      if (!this.dataReportAuth) {
        this.$message.warning('您没有敏感数据上报权限');
        return;
      }

      this.reportForm = {
        worker: '',
        reason: ''
      };

      this.reportDialogVisible = true;

      // 获取劳动者列表
      await this.getWorkerList();

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.reportForm) {
          this.$refs.reportForm.clearValidate();
        }
      });
    },

    // 提交上报
    async submitReport() {
      try {
        // 表单验证
        const valid = await this.$refs.reportForm.validate();
        if (!valid) return;

        this.reportLoading = true;

        // 找到选中的劳动者信息
        const selectedWorker = this.workerList.find(w => w._id === this.reportForm.worker);
        if (!selectedWorker) {
          this.$message.error('请选择有效的劳动者');
          return;
        }

        // 调用上报接口
        await createApprovalRecord({
          dataId: this.reportForm.worker,
          dataName: selectedWorker.name,
          dataModel: 'Employees',
          reportReason: this.reportForm.reason
        });

        this.$message.success('上报成功');
        this.reportDialogVisible = false;

        // 刷新列表数据
        this.getData();
      } catch (error) {
        this.$message.error(error.message || '上报失败');
        console.error(error);
      } finally {
        this.reportLoading = false;
      }
    }
  },

  // 监听路由变化
  watch: {
    '$route'(to) {
      if (to.name === 'sensitiveDataApproval') {
        this.getData();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.padding {
  padding: 20px;
}

.table {
  margin-top: 15px;
}

.label-title {
  font-size: 14px;
  margin-right: 0.5em;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

::v-deep .el-input {
  width: 250px;
}

::v-deep .el-select {
  width: 250px;
}

::v-deep .el-date-editor.el-input {
  width: 250px;
}

::v-deep .el-date-editor .el-range-input {
  margin-left: 15px;
}

.dataReportApproval {
  position: relative;
}

.first-line {
  display: flex;
  flex-wrap: wrap;

  .label-title {
    text-align: right;
    display: inline-block;
    width: 80px;
    font-size: 14px;
  }

  .search-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
  }
}

::v-deep .el-tag {
  border-radius: 28px;
}

::v-deep .dataReportApproval .table .el-tag {
  border-radius: 0px;
  text-align: center;
}

// 流程弹窗样式
.flow-content {
  .flow-header {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }

    .flow-reason {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .flow-steps {
    margin-bottom: 30px;
  }

  .flow-details {
    margin-top: 20px;
  }
}

.dialog-footer {
  text-align: center;
}

// 审批弹窗样式
.approval-content {
  .approval-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }

    p {
      margin: 5px 0;
      color: #666;
      font-size: 14px;

      strong {
        color: #333;
      }
    }
  }
}

// 抽屉内容样式
::v-deep .el-drawer__body {
  padding: 0;
}

::v-deep .el-drawer__header {
  padding: 20px 20px 0 20px;
  margin-bottom: 0;
}
</style>
