const Service = require('egg').Service;
const axios = require('axios')

class SensitiveDataApprovalService extends Service {

  constructor(ctx) {
    super(ctx);
    this.superUserInfo = ctx.session.superUserInfo;
    this.regAdd = this.superUserInfo.regAdd;
    console.log(this.superUserInfo, 'superUserInfo');
    console.log(this.regAdd, 'regAdd');
    this.itemCodeMap = {
      'Employees': 'worker_health_record_approval',
      'Adminorg': 'enterprise_health_record_approval'
    }
  }

  // 获取当前账号是否有上报权限
  async _getDataReportAuth() {
    const { ctx } = this;
    try {
      const superUserInfo = this.superUserInfo;
      const superUser = await ctx.model.SuperUser.findOne({ _id: superUserInfo._id });
      const { dataReportAuth } = superUser;
      return dataReportAuth;
    } catch (error) {
      console.log(error, 'error');
      throw new Error(error.message);
    }
  }


  // 根据管辖范围和劳动者户籍所在地获取劳动者列表
  async getEmployeeListByArea(params) {
    const { ctx } = this;
    try {
      const { pageNum = 1, pageSize = 10, keyword } = params;
      const query = { enable: true };
      if (keyword) {
        query['$or'] = [
          { name: new RegExp(keyword, 'i') },
          { IDNum: new RegExp(keyword, 'i') },
        ];
      }

      const match = {
        'employeeBasicInfo.nativePlace': { $all: this.regAdd }  // 匹配劳动者户籍所在地
      }

      const res = await ctx.model.Employee.aggregate([
        { $match: query },
        { $lookup: { from: "employeebasicinfos", localField: "IDNum", foreignField: "IDNum", as: "employeeBasicInfo" } },
        { $unwind: "$employeeBasicInfo" },
        { $match: match },
        {
          $facet: {
            list: [
              { $skip: (+pageNum - 1) * pageSize },
              { $limit: +pageSize },
            ],
            total: [{ $count: 'total' }],
          },
        },
        { $unwind: '$total' },
        { $project: { list: 1, total: '$total.total' } },
      ]);
      return res[0] || { list: [], total: 0 };
    } catch (error) {
      console.log(error, 'error');
      throw new Error(error.message);
    }
  }

  // 上报前，获取当前账号是否有上报权
  async getCanReport(params) {
    const { ctx } = this;
    try {

      // 先获取是否有上报权
      const dataReportAuth = await this._getDataReportAuth();
      // 如果没有上报权限，抛出异常
      if (!dataReportAuth) {
        throw new Error('当前账号未设置敏感数据上报权限，无法进行上报操作');
      }

      // 获取机构类型
      const info = await ctx.service.agreeSteps.getInsititutionInfo()
      if (!info) {
        throw new Error('获取当前机构信息失败');
      }
      const orgCategoryCode = info.orgTypes;
      // console.log(info, '🍊 info');

      // 根据上报类型获取上报流程
      const { dataModel } = params;
      const key = this.itemCodeMap[dataModel];

      const flow = await this.getAndValidateApprovalFlow({ key, orgCategoryCode })
      if (!flow) {
        throw new Error('当前没有可用的审批流程，无法发起上报');
      }
      // console.log(flow, '🍊 flow');
      return { dataReportAuth, info, flow };
    } catch (error) {
      console.log(error, 'error');
      throw new Error(error.message);
    }
  }

  // 创建敏感数据审批记录
  async createApprovalRecord(params) {
    const { ctx } = this;
    const { dataId, dataModel, reportReason } = params;

    // 先获取是否有上报权
    const dataReportAuth = await this._getDataReportAuth();
    // 如果没有上报权限，抛出异常
    if (!dataReportAuth) {
      throw new Error('当前账号未设置敏感数据上报权限，无法进行上报操作');
    }

    // 获取机构类型
    const info = await ctx.service.agreeSteps.getInsititutionInfo()
    if (!info) {
      throw new Error('获取当前机构信息失败');
    }

    // 根据上报类型获取上报流程
    const key = this.itemCodeMap[dataModel];
    const flow = await this.getAndValidateApprovalFlow({ key, orgCategoryCode: info.orgTypes })
    if (!flow) {
      throw new Error('当前没有可用的审批流程，无法发起上报');
    }

    // 判断dataModel，填入 dataCode 和 regAdd 如果是劳动者获取劳动者对应的户籍所在地，如果是企业获取企业注册地
    let dataName = '';
    let dataCode = '';
    let regAdd = [];
    if (dataModel === 'Employees') {
      const employee = await this.ctx.model.Employee.aggregate([
        { $match: { _id: dataId } },
        { $lookup: { from: "employeebasicinfos", localField: "IDNum", foreignField: "IDNum", as: "employeeBasicInfo" } },
        { $unwind: "$employeeBasicInfo" },
      ]);
      if (!employee || !employee[0] || !employee[0].employeeBasicInfo || !employee[0].employeeBasicInfo.nativePlace) {
        throw new Error('获取劳动者信息失败');
      }
      dataName = employee[0].name;
      regAdd = employee[0].employeeBasicInfo.nativePlace;
      dataCode = employee[0].IDNum;
    } else if (dataModel === 'Adminorg') {
      const adminorg = await ctx.model.Adminorg.findOne({ _id: dataId });
      dataName = adminorg.cname;
      regAdd = adminorg.districtRegAdd;
      dataCode = adminorg.code;
    }

    // 确定初始审批节点（从最高levelCode开始）
    const firstStep = flow.steps[0]; // 已经按levelCode降序排列

    const res = await ctx.model.SensitiveDataApproval.create({
      // 上报数据
      dataId,
      dataName,
      dataModel,
      reportReason,
      // 
      dataCode,
      regAdd,
      // 上报单位
      superUserId: this.superUserInfo._id,
      // 审批流程
      flowId: flow.flowId,
      steps: flow.steps,
      // 当前审批节点
      currentLevelCode: firstStep.levelCode,
      currentLevelName: firstStep.levelName,
      // 状态
      status: 0,
      process: []
    });

    return res;
  }

  // 获取敏感数据审批列表
  async getDataReportApprovalList(params) {
    const { ctx } = this;
    const {
      pageNum = 1,
      pageSize = 10,
      dataName,
      dataModel,
      status
    } = params;

    const query = {
      regAdd: { $all: this.regAdd },     // 根据账号管辖区域过滤数据
      dataModel
    };

    if (dataName) {
      query.dataName = new RegExp(dataName, 'i');
    }
    if (status) {
      query.status = +status;
    }

    const list = await this.ctx.model.SensitiveDataApproval
      .find(query)
      .sort({ createdAt: -1 })
      .skip((+pageNum - 1) * pageSize)
      .limit(+pageSize)
      .populate('superUserId', 'name cname')
      .select({ 'process': 0 })
      .lean();
    const total = await this.ctx.model.SensitiveDataApproval.countDocuments(query);

    // 获取机构级别
    const info = await ctx.service.agreeSteps.getInsititutionInfo()
    if (!info) {
      throw new Error('获取当前机构信息失败');
    }
    const { orgLevel } = info

    // 格式化返回数据
    const formattedList = list.map(item => ({
      ...item,
      canApprove: item.currentLevelCode === orgLevel, // 只有当前审批节点的机构可以审批
      canUpdate: (item.status === 0 || item.status === 3) && item.superUserId._id === this.superUserInfo._id, // 只有创建者可以修改，且只能在待审批和被拒绝时修改
    }));

    return { list: formattedList, total };
  }

  // 更新审批记录
  async updateApprovalRecord(params) {
    const { ctx } = this;
    const { _id, dataId, dataModel, reportReason } = params;

    const approval = await ctx.model.SensitiveDataApproval.findById(_id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 只有创建者可以更新，且只能更新未开始审批的记录
    if (approval.superUserId !== this.superUserInfo._id) {
      throw new Error('您没有权限更新此记录');
    }

    if (approval.status === 1) {
      throw new Error('进行中的记录无法更新');
    }
    if (approval.status === 2) {
      throw new Error('已完成的记录无法更新');
    }

    // 更新字段
    approval.dataId = dataId;
    approval.reportReason = reportReason;
    if (dataModel === 'Employees') {
      // 获取劳动者信息
      const employee = await ctx.model.Employees.findOne({ _id: dataId });
      approval.dataName = employee.name;
      approval.dataCode = employee.IDNum;
      approval.regAdd = employee.employeeBasicInfo.nativePlace;
    } else if (dataModel === 'Adminorg') {
      const adminorg = await ctx.model.Adminorg.findOne({ _id: dataId });
      approval.dataName = adminorg.cname;
      approval.dataCode = adminorg.code;
      approval.regAdd = adminorg.districtRegAdd;
    }

    const res = await approval.save();
    return res
  }

  // 删除审批记录
  async deleteApprovalRecord(params) {
    const { ctx } = this;
    const { _id } = params;

    const approval = await ctx.model.SensitiveDataApproval.findById(_id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 只有创建者可以删除，且只能删除未开始审批的记录
    if (approval.superUserId !== this.superUserInfo._id) {
      throw new Error('您没有权限删除此记录');
    }

    if (approval.status === 1) {
      throw new Error('进行中的记录无法撤销');
    }
    if (approval.status === 2) {
      throw new Error('已完成的记录无法撤销');
    }

    const res = await ctx.model.SensitiveDataApproval.findByIdAndUpdate(id, { isDelete: true });
    return res;
  }

  /**
   * 获取审批流程详情
   * @param {Object} params - 查询参数
   * @returns {Object} 流程详情
   */
  async getApprovalFlowDetail(params) {
    const { id } = params;

    const approval = await this.ctx.model.SensitiveDataApproval
      .findById(id)
      .populate('process.approver', 'cname name')
      .populate('superUserId', 'cname name')
      .lean();

    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 使用实际的审批步骤
    const approvalSteps = approval.steps || [];

    // 计算当前步骤
    let currentStep = 0;
    if (approval.status === 2) {
      currentStep = approvalSteps.length; // 已完成所有步骤
    } else if (approval.status === 3) {
      currentStep = approval.process.length; // 拒绝时显示到拒绝的步骤
    } else {
      currentStep = approval.process.length; // 当前进行到的步骤
    }

    // 格式化审批历史
    const approvalHistory = approval.process.map(item => ({
      nodeName: item.level,
      approverName: item.approver ? item.approver.cname : '',
      approvalTime: item.time,
      approvalResult: item.result ? '通过' : '驳回',
      approvalComment: item.comment
    }));

    return {
      currentStep,
      approvalSteps: approvalSteps.map(step => ({
        nodeName: step.levelName,
        description: `${step.levelName}级审核`
      })),
      approvalHistory
    };
  }

  /**
   * 提交审批结果
   * @param {Object} params - 审批参数
   * @returns {Object} 审批结果
   */
  async submitApprovalResult(params) {
    const { id, result, comment, superUserInfo } = params;

    const approval = await this.ctx.model.SensitiveDataApproval.findById(id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    if (approval.status === 2) {
      throw new Error('该记录已审批完成，无法重复审批');
    }

    if (approval.status === 3) {
      throw new Error('该记录已被拒绝，无法重复审批');
    }

    // 检查是否有权限审批
    if (!this.canApprove(approval, superUserInfo)) {
      throw new Error('您没有权限审批此记录');
    }

    const currentStepIndex = approval.process.length;
    const currentStep = approval.steps[currentStepIndex];

    // 添加审批记录
    const approvalRecord = {
      approver: superUserInfo._id,
      time: new Date(),
      result: result === 'approve',
      comment: comment,
      level: currentStep.levelName
    };

    approval.process.push(approvalRecord);

    // 更新审批状态和当前节点
    if (result === 'reject') {
      // 审批被拒绝，重新获取最新的审批流程
      const flowInfo = await this.getAndValidateApprovalFlow(approval.dataModel);
      approval.flowId = flowInfo.flowId;
      approval.steps = flowInfo.steps;
      approval.currentNode = flowInfo.steps[0].levelName; // 重新从第一步开始
      approval.status = 3; // 设置为拒绝状态
      approval.process = [approvalRecord]; // 清空之前的审批记录，只保留当前拒绝记录
    } else {
      // 审批通过
      const nextStepIndex = currentStepIndex + 1;
      if (nextStepIndex >= approval.steps.length) {
        // 已完成所有审批步骤
        approval.status = 2;
        approval.currentNode = '已完成';
      } else {
        // 更新到下一个审批节点
        approval.status = 1; // 审批中
        approval.currentNode = approval.steps[nextStepIndex].levelName;
      }
    }

    await approval.save();

    return { success: true, message: '审批提交成功' };
  }



  // 辅助方法
  getApprovalStatus(item) {
    if (item.status === 2) return 'approved'; // 审批完成
    if (item.status === 3) return 'rejected'; // 审批被拒绝
    if (item.status === 1) return 'processing'; // 审批中
    return 'pending'; // 待审批
  }

  getUserLevel(superUserInfo) {
    // 根据用户regAdd管辖区域判断级别
    const { regAdd } = superUserInfo;
    if (!regAdd || !Array.isArray(regAdd)) {
      return null;
    }

    // 判断是否包含"建设兵团"
    const hasXJBT = regAdd.includes('建设兵团');
    const regAddLength = regAdd.length;

    if (hasXJBT) {
      if (regAddLength === 1) {
        // 只有"建设兵团"，为兵团级
        return '1'; // 兵团级对应levelCode为1
      } else if (regAddLength === 2) {
        // ["建设兵团", "建设兵团第一师"]，为师市级
        return '2'; // 师市级对应levelCode为2
      } else if (regAddLength === 3) {
        // ["建设兵团", "建设兵团第一师", "某团场"]，为团场级
        return '3'; // 团场级对应levelCode为3
      }
    }

    // 如果不包含"建设兵团"，根据长度判断
    if (regAddLength === 1) {
      return '4'; // 连队级对应levelCode为4
    }

    return null;
  }



  // 获取企业列表（用于上报时搜索）
  async getEnterpriseList(params) {
    const { pageNum = 1, pageSize = 10, cname, superUserInfo } = params;
    const query = {
      isDelete: false,
      productionStatus: { $ne: '0' } // 排除注销状态
    };

    // 根据管辖区域筛选企业
    const { regAdd } = superUserInfo;
    if (regAdd && regAdd.length > 0) {
      query['workAddress.districts'] = { $all: regAdd };
    }

    if (cname) {
      query.cname = new RegExp(cname, 'i');
    }

    const list = await this.ctx.model.Adminorg
      .find(query)
      .sort({ createdAt: -1 })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .select('_id cname workAddress industryCategory')
      .lean();

    const total = await this.ctx.model.Adminorg.countDocuments(query);

    // 格式化返回数据
    const formattedList = list.map(item => ({
      id: item._id,
      name: item.cname,
      address: item.workAddress ? item.workAddress.districts.join(' ') : '',
      industryCategory: item.industryCategory || ''
    }));

    return { list: formattedList, total };
  }

  // 获取并验证审批流程
  async getAndValidateApprovalFlow(params) {
    const { app } = this
    let { dataModel, key, orgCategoryCode } = params;
    if (dataModel) {
      key = dataModel === 'Employees' ? 'worker_health_record_approval' : 'enterprise_health_record_approval';
    }

    const result = await axios({
      url: `${app.config.domainNames.portal}/api/uaProcess/versions/current`,
      method: 'get',
      params: { itemCode: key, orgCategoryCode }
    })

    if (result.data.status !== 200) { // 审批流获取失败
      throw new Error(result.data.message);
    }
    const flowData = result.data.data;


    if (!flowData || !flowData.steps || flowData.steps.length === 0) {
      throw new Error('当前没有可用的审批流程，无法提交上报');
    }

    // 按levelCode排序审批步骤（4->3->2->1）
    const sortedSteps = flowData.steps.sort((a, b) => parseInt(b.levelCode) - parseInt(a.levelCode));

    return {
      flowId: flowData._id,
      steps: sortedSteps,
    };
  }
  // 接口示例
  // https://xjbtportal.jkqy.cn/api/uaProcess/versions/current?itemCode=worker_health_record_approval&orgCategoryCode=100
  // {
  //   "status": 200,
  //     "data": {
  //     "_id": "d1fb9ca1-17b3-4cdf-bb1c-4d395b9f6e75", // 流程id
  //       "orgCategoryCodes": [
  //         "120",
  //         "121",
  //         "100"
  //       ],
  //         "templateId": "96sG4jigw",
  //           "itemCode": "worker_health_record_approval",
  //             "steps": [ // 审批步骤
  //               {
  //                 "order": 1, //  顺序（无实际意义，可忽略
  //                 "levelCode": "3", // 审批级别 目前固定 1-4级，从第4级开始，可以按照这个排序，实际也是按照这个审批而不是order
  //                 "levelName": "团场/区县级"
  //               },
  //               {
  //                 "order": 2,
  //                 "levelCode": "2",
  //                 "levelName": "师市级"
  //               },
  //               {
  //                 "order": 3,
  //                 "levelCode": "1",
  //                 "levelName": "兵团/省级"
  //               },
  //               {
  //                 "order": 4,
  //                 "levelCode": "4",
  //                 "levelName": "连队/乡镇级"
  //               }
  //             ],
  //               "itemName": "“一人一档”敏感数据上报与审批", // 审批流程名称
  //                 "orgCategoryNames": [
  //                   "疾病预防控制中心",
  //                   "职业病防治院所",
  //                   "卫生行政管理部门"
  //                 ]
  //   },
  //   "message": ""
  // }

}

module.exports = SensitiveDataApprovalService;
