
exports.jk_eHealthRecord = {
  alias: 'eHealthRecord', // 插件目录，必须为英文
  pkgName: 'egg-jk-eHealthRecord', // 插件包名
  enName: 'jk_eHealthRecord', // 插件名
  name: '电子健康档案', // 插件名称
  description: '考试大纲管理', // 插件描述
  adminApi: [
    {
      url: 'eHealthRecord/getEmployeeList',
      method: 'post',
      controllerName: 'getEmployeeList',
      details: '获取当前企业员工列表',
    },
    {
      url: 'eHealthRecord/getEmployeeDetail',
      method: 'get',
      controllerName: 'getEmployeeDetail',
      details: '获取劳动者体检报告详情',
    },
    {
      url: 'eHealthRecord/getEmployeeHCReport',
      method: 'get',
      controllerName: 'getEmployeeHCReport',
      details: '获取劳动者体检报告',
    },
    {
      url: 'eHealthRecord/createEHealthRecordAuth',
      method: 'post',
      controllerName: 'createEHealthRecordAuth',
      details: '企业向劳动者发起授权申请',
    },
    {
      url: 'eHealthRecord/getEHealthRecordAuthList',
      method: 'post',
      controllerName: 'getEHealthRecordAuthList',
      details: '当前企业申请授权列表',
    },
    {
      url: 'eHealthRecord/cancelEHealthRecordAuth',
      method: 'get',
      controllerName: 'cancelEHealthRecordAuth',
      details: '删除授权申请',
    },
    {
      url: 'eHealthRecord/getEHealthRecordList',
      method: 'post',
      controllerName: 'getEHealthRecordList',
      details: '获取历史健康档案列表',
    },
    {
      url: 'eHealthRecord/createSaveCondition',
      method: 'post',
      controllerName: 'createSaveCondition',
      details: '创建健康档案搜索条件',
    },
    {
      url: 'eHealthRecord/getSaveCondition',
      method: 'get',
      controllerName: 'getSaveCondition',
      details: '获取健康档案搜索条件列表',
    },
    {
      url: 'eHealthRecord/deleteSaveCondition',
      method: 'post',
      controllerName: 'deleteSaveCondition',
      details: '删除健康档案搜索条件',
    },
    {
      url: 'eHealthRecord/exportExcel',
      method: 'post',
      controllerName: 'exportExcel',
      details: '导出健康档案',
    },
    {
      url: 'eHealthRecord/downloadEHealthRecordList',
      method: 'get',
      controllerName: 'downloadEHealthRecordList',
      details: '下载筛选的历史健康档案数据',
    },
    {
      url: 'eHealthRecord/getEHealthRecordDetail',
      method: 'get',
      controllerName: 'getEHealthRecordDetail',
      details: '获取历史健康档案详情',
    },
    {
      url: 'eHealthRecord/getDistrict',
      method: 'get',
      controllerName: 'getDistrict',
      details: '获取地区',
    },
    {
      url: 'eHealthRecord/getSupervisionList',
      method: 'get',
      controllerName: 'getSupervisionList',
      details: '监管单位查询',
    },
    {
      url: 'eHealthRecord/postComplaint',
      method: 'get',
      controllerName: 'postComplaint',
      details: '提出申诉',
    },
    {
      url: 'eHealthRecord/getEHealthRecordBaseInfo',
      method: 'get',
      controllerName: 'getEHealthRecordBaseInfo',
      details: '获取个人基本信息',
    },
    {
      url: 'eHealthRecord/updateEHealthRecordBaseInfo',
      method: 'post',
      controllerName: 'updateEHealthRecordBaseInfo',
      details: '编辑电子健康档案基本信息',
    },
    {
      url: 'eHealthRecord/getComplaintListByOrg',
      method: 'post',
      controllerName: 'getComplaintListByOrg',
      details: '当前单位提出的申诉列表',
    },
    {
      url: 'eHealthRecord/getUserComplaintList',
      method: 'post',
      controllerName: 'getUserComplaintList',
      details: '当前单位待劳动者申诉列表',
    },
    {
      url: 'eHealthRecord/handleComplaint',
      method: 'post',
      controllerName: 'handleComplaint',
      details: '处理申诉',
    },
    // #region 统计
    {
      url: 'eHealthRecord/getStatisticsByYear',
      method: 'get',
      controllerName: 'getStatisticsByYear',
      details: '按照年度统计',
    },
    {
      url: 'eHealthRecord/getStatisticsByYearAndHarmFactor',
      method: 'get',
      controllerName: 'getStatisticsByYearAndHarmFactor',
      details: '按照年度统计每种危害因素',
    },
    // 新增职业健康检查统计接口
    {
      url: 'eHealthRecord/getHealthCheckEnterpriseStats',
      method: 'get',
      controllerName: 'getHealthCheckEnterpriseStats',
      details: '职业健康检查开展情况统计',
    },
    {
      url: 'eHealthRecord/getHealthCheckTypeStats',
      method: 'get',
      controllerName: 'getHealthCheckTypeStats',
      details: '职业健康检查类别统计情况',
    },
    {
      url: 'eHealthRecord/getHealthCheckEmployeeStats',
      method: 'get',
      controllerName: 'getHealthCheckEmployeeStats',
      details: '职业健康检查员工统计情况',
    },
    {
      url: 'eHealthRecord/getHealthCheckResultStats',
      method: 'get',
      controllerName: 'getHealthCheckResultStats',
      details: '职业健康检查结果情况',
    },
    {
      url: 'eHealthRecord/getHazardFactorCategories',
      method: 'get',
      controllerName: 'getHazardFactorCategories',
      details: '职业健康检查危害因素统计',
    },
    {
      url: 'eHealthRecord/findHarmFactors',
      method: 'get',
      controllerName: 'findHarmFactors',
      details: '查询危害因素列表',
    },
    // 电子健康档案统计分析
    {
      url: 'eHealthRecord/getERecordStatisticByArea',
      method: 'post',
      controllerName: 'getERecordStatisticByArea',
      details: '电子健康档案统计分析地区分布',
    },
    {
      url: 'eHealthRecord/getERecordStatisticByTime',
      method: 'post',
      controllerName: 'getERecordStatisticByTime',
      details: '电子健康档案统计分析时间分布',
    },
    {
      url: 'eHealthRecord/getERecordStatisticByRange',
      method: 'post',
      controllerName: 'getERecordStatisticByRange',
      details: '电子健康档案统计分析范围分布',
    },
    {
      url: 'eHealthRecord/getERecordStatisticExcel',
      method: 'post',
      controllerName: 'getERecordStatisticExcel',
      details: '生成电子健康档案统计分析报表',
    },
    // #endregion
    // #region 职业史
    {
      url: 'eHealthRecord/getEmploymentHistory',
      method: 'get',
      controllerName: 'getEmploymentHistory',
      details: '获取劳动者职业史',
    },
    {
      url: 'eHealthRecord/addEmploymentHistory',
      method: 'post',
      controllerName: 'addEmploymentHistory',
      details: '新增一条劳动者职业史',
    },
    {
      url: 'eHealthRecord/editEmploymentHistory',
      method: 'post',
      controllerName: 'editEmploymentHistory',
      details: '编辑劳动者职业史',
    },
    {
      url: 'eHealthRecord/deleteEmploymentHistory',
      method: 'post',
      controllerName: 'deleteEmploymentHistory',
      details: '删除一条劳动者职业史',
    },
    // #endregion
    // #region 诊断鉴定
    {
      url: 'eHealthRecord/getDiaList',
      method: 'post',
      controllerName: 'getDiaList',
      details: '获取诊断记录列表',
    },
    {
      url: 'eHealthRecord/getIdentificationList',
      method: 'post',
      controllerName: 'getIdentificationList',
      details: '获取鉴定记录列表',
    },
    // #endregion
    // #region 敏感数据审批
    {
      url: 'sensitiveDataApproval/getEmployeeListByArea',
      method: 'get',
      controllerName: 'getEmployeeListByArea',
      details: '根据管辖范围和劳动者户籍所在地获取劳动者列表',
    },
    {
      url: 'sensitiveDataApproval/getCanReport',
      method: 'get',
      controllerName: 'getCanReport',
      details: '上报前，获取当前账号是否有上报权',
    },
    {
      url: 'sensitiveDataApproval/getDataReportApprovalList',
      method: 'post',
      controllerName: 'getDataReportApprovalList',
      details: '获取敏感数据审批列表',
    },
    {
      url: 'sensitiveDataApproval/getApprovalFlowDetail',
      method: 'get',
      controllerName: 'getApprovalFlowDetail',
      details: '获取审批流程详情',
    },
    {
      url: 'sensitiveDataApproval/submitApprovalResult',
      method: 'post',
      controllerName: 'submitApprovalResult',
      details: '提交审批结果',
    },
    {
      url: 'sensitiveDataApproval/createApprovalRecord',
      method: 'post',
      controllerName: 'createApprovalRecord',
      details: '敏感数据上报',
    },
    {
      url: 'sensitiveDataApproval/updateApprovalRecord',
      method: 'post',
      controllerName: 'updateApprovalRecord',
      details: '更新上报内容',
    },
    {
      url: 'sensitiveDataApproval/deleteApprovalRecord',
      method: 'get',
      controllerName: 'deleteApprovalRecord',
      details: '撤销上报',
    },

    // #endregion
  ],
  fontApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_eHealthRecord = {\n
        enable: true,\n        package: 'egg-jk-eHealthRecord',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  eHealthRecordRouter:{\n
        match: [
          ctx => ctx.path.startsWith('/manage/eHealthRecord') , 
          ctx.path.startsWith('/manage/sensitiveDataApproval')
        ],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: ['docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar'],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/propagate',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

